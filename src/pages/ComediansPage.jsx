import React, { useState } from 'react';
import { useAppContext } from '../context/AppContext';

function ComediansPage({ navigateTo }) {
  const { data } = useAppContext();
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = ['all', ...new Set(data.comedians.map(c => c.category))];
  const filteredComedians = selectedCategory === 'all'
    ? data.comedians
    : data.comedians.filter(c => c.category === selectedCategory);

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-800 mb-4">芸人一覧</h2>
        <p className="text-gray-600">お気に入りの芸人を見つけよう</p>
      </div>

      {/* カテゴリフィルター */}
      <div className="flex flex-wrap justify-center gap-3">
        {categories.map(category => (
          <button
            key={category}
            onClick={() => setSelectedCategory(category)}
            className={`px-6 py-2 rounded-full text-sm font-medium transition-all ${selectedCategory === category
              ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-lg'
              : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-200'
              }`}
          >
            {category === 'all' ? 'すべて' : category}
          </button>
        ))}
      </div>

      {/* 芸人カード */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {filteredComedians.map(comedian => (
          <div key={comedian.id} className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all transform hover:-translate-y-2">
            <div className="relative">
              <img
                src={comedian.image}
                alt={comedian.name}
                className="w-full h-48 object-cover"
              />
              <div className="absolute top-4 right-4 bg-gradient-to-r from-purple-600 to-blue-600 px-3 py-1 rounded-full shadow-lg">
                <span className="text-sm font-medium text-white">{comedian.category}</span>
              </div>
            </div>
            <div className="p-6">
              <h3 className="text-xl font-bold text-gray-800 mb-2">{comedian.name}</h3>
              <p className="text-gray-600 mb-4 line-clamp-2">{comedian.bio}</p>
              <div className="flex items-center justify-between">
                <div className="flex space-x-2">
                  {comedian.socialLinks.slice(0, 2).map((link, index) => (
                    <div key={index} className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                      <span className="text-xs text-gray-600">
                        {link.platform === 'Twitter' ? '🐦' :
                          link.platform === 'Instagram' ? '📷' :
                            link.platform === 'YouTube' ? '🎥' : '🔗'}
                      </span>
                    </div>
                  ))}
                </div>
                <button
                  onClick={() => navigateTo('comedian-detail', comedian.id)}
                  className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-2 rounded-full text-sm hover:from-purple-700 hover:to-blue-700 transition-all"
                >
                  詳細を見る
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default ComediansPage;
