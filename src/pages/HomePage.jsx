import React from 'react';
import { useAppContext } from '../context/AppContext';

function HomePage({ navigateTo }) {
  const { data } = useAppContext();
  const featuredComedian = data.comedians[0];

  return (
    <div className="space-y-8">
      {/* ヒーローセクション */}
      <div className="bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 rounded-3xl p-8 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-10"></div>
        <div className="relative z-10">
          <h2 className="text-4xl font-bold mb-4">お笑い芸人の情報が満載！</h2>
          <p className="text-xl opacity-90 mb-6">ファンによる、ファンのための情報共有サイト</p>
          <button
            onClick={() => navigateTo('comedians')}
            className="bg-white text-purple-600 px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-all transform hover:scale-105"
          >
            芸人一覧を見る
          </button>
        </div>
        <div className="absolute right-0 top-0 w-64 h-64 bg-white opacity-10 rounded-full -mr-32 -mt-32"></div>
        <div className="absolute right-20 bottom-0 w-32 h-32 bg-white opacity-10 rounded-full -mb-16"></div>
      </div>

      {/* 注目の芸人 */}
      <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
            <span className="text-white text-sm">⭐</span>
          </div>
          <h3 className="text-2xl font-bold text-gray-800">本日の注目芸人</h3>
        </div>
        <div className="flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-8">
          <div className="relative">
            <img
              src={featuredComedian.image}
              alt={featuredComedian.name}
              className="w-32 h-32 object-cover rounded-2xl shadow-lg ring-4 ring-purple-100"
            />
            <div className="absolute -top-2 -right-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold">
              1
            </div>
          </div>
          <div className="flex-1 text-center md:text-left">
            <h4 className="text-2xl font-bold text-gray-800 mb-2">{featuredComedian.name}</h4>
            <p className="text-gray-600 mb-4">{featuredComedian.bio}</p>
            <button
              onClick={() => navigateTo('comedian-detail', featuredComedian.id)}
              className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-6 py-3 rounded-full hover:from-purple-700 hover:to-blue-700 transition-all transform hover:scale-105 shadow-lg"
            >
              詳細を見る
            </button>
          </div>
        </div>
      </div>

      {/* 統計情報 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div
          className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 text-center cursor-pointer hover:shadow-xl transition-all transform hover:-translate-y-1"
          onClick={() => navigateTo('comedians')}
        >
          <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-white text-xl">👥</span>
          </div>
          <h4 className="text-2xl font-bold text-gray-800 mb-2">{data.comedians.length}</h4>
          <p className="text-gray-600">登録芸人数</p>
        </div>
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 text-center">
          <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-white text-xl">📅</span>
          </div>
          <h4 className="text-2xl font-bold text-gray-800 mb-2">{data.events.filter(e => e.isApproved).length}</h4>
          <p className="text-gray-600">出演情報</p>
        </div>
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 text-center">
          <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-white text-xl">📰</span>
          </div>
          <h4 className="text-2xl font-bold text-gray-800 mb-2">{data.articles.filter(a => a.isApproved).length}</h4>
          <p className="text-gray-600">記事数</p>
        </div>
      </div>

      {/* お知らせ */}
      <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-2xl p-8 border border-indigo-100">
        <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
          <span className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center mr-3">
            <span className="text-white text-sm">📢</span>
          </span>
          最新情報
        </h3>
        <div className="space-y-4">
          <div className="flex items-start space-x-4">
            <div className="w-2 h-2 bg-purple-500 rounded-full mt-3"></div>
            <div>
              <p className="text-gray-800 font-medium">✨ 新機能：芸人詳細ページでSNSリンクを確認できるようになりました</p>
              <p className="text-gray-600 text-sm">2024年12月1日</p>
            </div>
          </div>
          <div className="flex items-start space-x-4">
            <div className="w-2 h-2 bg-blue-500 rounded-full mt-3"></div>
            <div>
              <p className="text-gray-800 font-medium">📢 投稿された情報は管理者の承認後に公開されます</p>
              <p className="text-gray-600 text-sm">システム情報</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default HomePage;
